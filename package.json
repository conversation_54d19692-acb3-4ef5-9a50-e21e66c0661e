{"name": "shopify-headless-store", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "prisma generate && next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "postinstall": "prisma generate"}, "dependencies": {"@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.10.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@shopify/hydrogen-react": "^2025.1.3", "@shopify/shopify-api": "^11.12.0", "@shopify/storefront-api-client": "^1.0.7", "@types/bcrypt": "^5.0.2", "@types/nodemailer": "^6.4.17", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "glob": "^10.4.5", "graphql-request": "^7.1.2", "lucide-react": "^0.510.0", "next": "^14.2.18", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.36"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8", "eslint-config-next": "14.2.18", "postcss": "^8", "prisma": "^6.10.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}