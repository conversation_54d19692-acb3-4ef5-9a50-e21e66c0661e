import { NextRequest, NextResponse } from 'next/server';
import { testEmailConfig, sendQuoteEmail } from '@/lib/email';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Test endpoint only available in development' },
        { status: 403 }
      );
    }

    console.log('Testing email configuration...');

    // First test the configuration
    const configResult = await testEmailConfig();
    console.log('Config test result:', configResult);

    if (!configResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Email configuration failed',
        details: configResult.message,
        timestamp: new Date().toISOString()
      });
    }

    // If config is good, try sending a test email
    console.log('Sending test email...');

    const testQuoteData = {
      quoteId: 'TEST-' + Date.now(),
      products: [{
        id: 'test-product',
        title: 'Test Product',
        quantity: 1,
        price: 10.00
      }],
      customer: {
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '+216 12 345 678'
      },
      totals: {
        totalQuantity: 1,
        subtotal: 10.00,
        discount: 0,
        discountAmount: 0,
        total: 10.00
      }
    };

    const emailResult = await sendQuoteEmail(testQuoteData);
    console.log('Email result:', emailResult);

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully!',
      configTest: configResult,
      emailTest: emailResult,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test email error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Email test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
