import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Config check only available in development' },
        { status: 403 }
      );
    }

    const config = {
      hasSmtpHost: !!process.env.SMTP_HOST,
      hasSmtpPort: !!process.env.SMTP_PORT,
      hasSmtpUser: !!process.env.SMTP_USER,
      hasSmtpPass: !!process.env.SMTP_PASS,
      smtpHost: process.env.SMTP_HOST || 'NOT_SET',
      smtpPort: process.env.SMTP_PORT || 'NOT_SET',
      smtpUser: process.env.SMTP_USER || 'NOT_SET',
      smtpPassLength: process.env.SMTP_PASS?.length || 0,
    };

    const allConfigured = config.hasSmtpHost && config.hasSmtpPort && config.hasSmtpUser && config.hasSmtpPass;

    return NextResponse.json({
      configured: allConfigured,
      config,
      message: allConfigured 
        ? 'Email configuration looks good!' 
        : 'Missing email configuration. Please check your .env file.',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to check email configuration',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
