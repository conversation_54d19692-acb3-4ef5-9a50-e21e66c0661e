'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function TestSignupPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: ''
  });
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setResponse(null);

    try {
      console.log('🚀 Sending signup request:', formData);
      
      const res = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await res.json();
      
      console.log('📥 Response status:', res.status);
      console.log('📥 Response data:', data);
      
      setResponse({
        status: res.status,
        ok: res.ok,
        data: data
      });
    } catch (error) {
      console.error('❌ Request failed:', error);
      setResponse({
        status: 'ERROR',
        ok: false,
        data: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Test Signup API</h1>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter your name"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Enter your password"
              required
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={loading}
          >
            {loading ? 'Testing...' : 'Test Signup'}
          </Button>
        </form>

        {/* Quick Test Buttons */}
        <div className="mt-6 space-y-2">
          <h3 className="font-semibold">Quick Tests:</h3>
          
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => setFormData({
              name: 'Test User',
              email: `test${Date.now()}@example.com`,
              password: 'TestPassword123!'
            })}
          >
            Fill Valid Data
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => setFormData({
              name: '',
              email: 'invalid-email',
              password: '123'
            })}
          >
            Fill Invalid Data
          </Button>
        </div>

        {/* Response Display */}
        {response && (
          <div className="mt-6 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-semibold mb-2">Response:</h3>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Status:</strong> 
                <span className={`ml-2 px-2 py-1 rounded ${
                  response.ok ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {response.status}
                </span>
              </div>
              <div>
                <strong>Data:</strong>
                <pre className="mt-1 p-2 bg-white rounded border text-xs overflow-auto">
                  {JSON.stringify(response.data, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* Environment Info */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-2">Environment Info:</h3>
          <div className="text-sm space-y-1">
            <div>Database URL: {process.env.DATABASE_URL ? '✅ Set' : '❌ Missing'}</div>
            <div>Node Env: {process.env.NODE_ENV}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
