# Authentication Setup & Troubleshooting Guide

## 🚨 Current Issues & Solutions

### Issue 1: Credential Signup Not Working

**Possible Causes:**
1. Database connection issues
2. Password hashing problems
3. Validation errors
4. Environment variables missing

**Solutions:**

#### Check Database Connection
```bash
# Test database connection
npx prisma db push
npx prisma generate
```

#### Verify Environment Variables
Ensure these are set in production:
```env
DATABASE_URL="your_production_database_url"
NEXTAUTH_SECRET="your_secure_secret_minimum_32_chars"
```

#### Test Signup API Directly
```bash
curl -X POST https://your-domain.vercel.app/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "password": "TestPassword123"
  }'
```

### Issue 2: Google OAuth "Se connecter avec Google" Error

**Error:** OAuth configuration issues in production

**Root Cause:** Google OAuth redirect URIs not properly configured

**Solutions:**

#### 1. Configure Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Credentials"
4. Edit your OAuth 2.0 Client ID
5. Add these Authorized redirect URIs:
   ```
   https://your-domain.vercel.app/api/auth/callback/google
   http://localhost:3000/api/auth/callback/google (for development)
   ```

#### 2. Update Environment Variables
In Vercel dashboard or .env.local:
```env
NEXTAUTH_URL=https://your-domain.vercel.app
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

#### 3. Verify OAuth Configuration
The auth.ts file should have:
```typescript
Google({
  clientId: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  authorization: {
    params: {
      prompt: "consent",
      access_type: "offline", 
      response_type: "code",
    },
  },
})
```

## 🔧 Production Deployment Checklist

### Environment Variables (Vercel)
1. Set `NEXTAUTH_URL` to your production domain
2. Set `NEXTAUTH_SECRET` (generate with: `openssl rand -base64 32`)
3. Configure Google OAuth credentials
4. Set database URL
5. Configure email settings

### Google OAuth Setup
1. **Authorized JavaScript origins:**
   - `https://your-domain.vercel.app`
   - `http://localhost:3000` (development)

2. **Authorized redirect URIs:**
   - `https://your-domain.vercel.app/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (development)

### Database Setup
1. Ensure database is accessible from Vercel
2. Run migrations: `npx prisma db push`
3. Generate client: `npx prisma generate`

## 🐛 Debugging Steps

### 1. Check Vercel Logs
```bash
vercel logs your-deployment-url
```

### 2. Test Authentication Endpoints
```bash
# Test signup
curl -X POST https://your-domain.vercel.app/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","password":"Test123456"}'

# Test signin
curl -X POST https://your-domain.vercel.app/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123456"}'
```

### 3. Verify Environment Variables
In Vercel dashboard, check all environment variables are set correctly.

### 4. Check Database Connection
Ensure your database allows connections from Vercel's IP ranges.

## 🚀 Quick Fix Commands

### Regenerate Prisma Client
```bash
npx prisma generate
npx prisma db push
```

### Test Local Authentication
```bash
npm run dev
# Test at http://localhost:3000/auth/signin
```

### Redeploy with Fresh Environment
```bash
vercel --prod
```

## 📞 Support

If issues persist:
1. Check Vercel deployment logs
2. Verify Google Cloud Console settings
3. Test database connectivity
4. Ensure all environment variables are set
5. Check NextAuth.js documentation for latest updates
