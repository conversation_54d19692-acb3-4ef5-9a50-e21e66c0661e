#!/usr/bin/env node

/**
 * Authentication Test Script
 * Tests both credential signup and Google OAuth configuration
 */

const https = require('https');
const http = require('http');

// Configuration
const DOMAIN = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const TEST_USER = {
  name: 'Test User',
  email: `test-${Date.now()}@example.com`,
  password: 'TestPassword123!'
};

console.log('🧪 Testing Authentication System...\n');
console.log(`Domain: ${DOMAIN}\n`);

// Test 1: Signup API
function testSignup() {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify(TEST_USER);
    const url = new URL('/api/auth/signup', DOMAIN);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    req.write(data);
    req.end();
  });
}

// Test 2: Check Google OAuth configuration
function testGoogleOAuth() {
  return new Promise((resolve, reject) => {
    const url = new URL('/api/auth/providers', DOMAIN);
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'GET'
    };

    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(body);
          resolve({ status: res.statusCode, data: response });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
}

// Run tests
async function runTests() {
  try {
    // Test 1: Signup
    console.log('1️⃣ Testing Credential Signup...');
    const signupResult = await testSignup();
    
    if (signupResult.status === 201) {
      console.log('✅ Signup successful!');
      console.log(`   User created: ${signupResult.data.user?.email}`);
    } else if (signupResult.status === 400 && signupResult.data.error?.includes('already exists')) {
      console.log('⚠️  User already exists (this is expected if running multiple times)');
    } else {
      console.log('❌ Signup failed:');
      console.log(`   Status: ${signupResult.status}`);
      console.log(`   Error: ${signupResult.data.error || signupResult.data}`);
    }
    
    console.log('');

    // Test 2: Google OAuth
    console.log('2️⃣ Testing Google OAuth Configuration...');
    const oauthResult = await testGoogleOAuth();
    
    if (oauthResult.status === 200) {
      const providers = oauthResult.data;
      const googleProvider = providers.google;
      
      if (googleProvider) {
        console.log('✅ Google OAuth configured!');
        console.log(`   Provider ID: ${googleProvider.id}`);
        console.log(`   Provider Name: ${googleProvider.name}`);
        console.log(`   Signin URL: ${googleProvider.signinUrl}`);
      } else {
        console.log('❌ Google OAuth not found in providers');
        console.log('   Available providers:', Object.keys(providers));
      }
    } else {
      console.log('❌ Failed to fetch OAuth providers:');
      console.log(`   Status: ${oauthResult.status}`);
      console.log(`   Error: ${oauthResult.data}`);
    }

    console.log('\n🏁 Test completed!');
    
    // Environment check
    console.log('\n📋 Environment Check:');
    console.log(`   NEXTAUTH_URL: ${process.env.NEXTAUTH_URL ? '✅ Set' : '❌ Missing'}`);
    console.log(`   NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? '✅ Set' : '❌ Missing'}`);
    console.log(`   GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? '✅ Set' : '❌ Missing'}`);
    console.log(`   GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`);
    console.log(`   DATABASE_URL: ${process.env.DATABASE_URL ? '✅ Set' : '❌ Missing'}`);

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

runTests();
